package fr.enedis.i2r.comsi.metrics;

import static java.time.temporal.ChronoUnit.SECONDS;

import java.security.SecureRandom;
import java.time.Duration;
import java.time.LocalTime;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.errors.RequestToSiException;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;

public class MetricsSender implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(MetricsSender.class);

    private ComSiConfiguration configuration;
    private SiClientPort siClientPort;
    private MetricsSourcePort metricsSource;
    private ScheduledFuture<?> collectMetricsTaskFuture;
    private final ScheduledExecutorService scheduler;
    private ComsiParametersPort comsiParametersPort;

    public MetricsSender(ComSiConfiguration comSiConfiguration, ComsiParametersPort comsiParametersPort, SiClientPort siClientPort, MetricsSourcePort metricsSource) {
        this.configuration = comSiConfiguration;
        this.comsiParametersPort = comsiParametersPort;
        this.siClientPort = siClientPort;
        this.metricsSource = metricsSource;
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "MetricsSender");
            t.setDaemon(true);
            return t;
        });

        this.collectMetricsTaskFuture = scheduler.schedule(() -> {
        }, 0, TimeUnit.MILLISECONDS);
        this.collectMetricsTaskFuture.cancel(false);
    }

    @Override
    public void run() {
        var metricsSendTime = configuration.metricsSendTime().orElseGet(this::initializeMetricsSendTime);

        if (collectMetricsTaskFuture.isCancelled() || collectMetricsTaskFuture.isDone()) {
            collectMetricsTaskFuture = scheduler.scheduleAtFixedRate(
                this::sendMetrics,
                getInitialDelaySeconds(metricsSendTime),
                Duration.ofDays(1).toSeconds(),
                TimeUnit.SECONDS
            );
        }
    }

    public void sendMetrics() {
        try {
            logger.info("Démarrage de l'envoi des métriques au SI. Récupération des métriques disponibles.");
            List<MetricsSet> metricsSets = this.metricsSource.getAvailableMetrics();

            logger.info(String.format("Nombre d'ensembles de métriques disponibles à l'envoi : %d", metricsSets.size()));

            for (MetricsSet metricsSet : metricsSets) {
                handleMetricsSet(metricsSet);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
    }

    private void handleMetricsSet(MetricsSet metricsSet) {
        try {
            this.siClientPort.sendMetricsBoitier(metricsSet);
            this.metricsSource.removeMetrics(metricsSet);
        } catch(RequestToSiException e) {
            logger.error("échec de l'envoi de la requête au SI", e);
        } catch(Exception e) {
            logger.error("une erreur est survenue lors de l'envoi des métriques", e);
        }
    }

    private LocalTime initializeMetricsSendTime() {
        Duration intervalMaximumLength = configuration.metricsSendIntervalMaximumLength();
        var random = new SecureRandom();
        Duration delayFromStartInterval = Duration.ofMinutes(random.nextLong(intervalMaximumLength.toMinutes()));
        LocalTime metricsSendTime = configuration.metricsSendIntervalStart().plusMinutes(delayFromStartInterval.toMinutes());
        comsiParametersPort.updateMetricsSendTime(metricsSendTime);
        return metricsSendTime;
    }

    /**
     * Calcul du délai nécessaire avant le lancement du premier job apès un (re-) démarrage en fonction de si l'horaire d'exécution est déjà passé pour
     * la journée en cours
     *
     * @param targetTime l'heure ciblé
     * @return le délai en minute avant la prochaine occurrence de {@code targetTime}
     */
    private long getInitialDelaySeconds(LocalTime targetTime) {
        LocalTime now = LocalTime.now();
        long reportDemain = 0L;
        if (now.isAfter(targetTime)) {
            reportDemain = Duration.ofDays(1).toSeconds();
        }
        return now.until(targetTime, SECONDS) + reportDemain;
    }
}
