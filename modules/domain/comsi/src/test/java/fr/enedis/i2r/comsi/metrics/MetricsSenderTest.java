package fr.enedis.i2r.comsi.metrics;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.time.LocalTime;
import java.util.Optional;

import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.CustomComSiConfiguration;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;

class MetricsSenderTest {

    public static final ComsiParametersPort parametersPort = mock(ComsiParametersPort.class);
    public static final SiClientPort siClientPort = mock(SiClientPort.class);
    public static final MetricsSourcePort metricsSource = mock(MetricsSourcePort.class);

    @Test
    void si_metrics_sending_time_est_definie_alors_sa_valeur_n_est_pas_change() {
        var conf = new CustomComSiConfiguration();
        conf.metricsSendingTime = Optional.of(LocalTime.now());
        var metricsSender = new MetricsSender(conf.build(), parametersPort, siClientPort, metricsSource);

        metricsSender.run();
        verify(parametersPort, never()).updateMetricsSendTime(any());
    }

    @Test
    void metrics_sending_time_est_initialise_si_aucune_valeur_n_est_definie() {
        var conf = new CustomComSiConfiguration();
        var metricsSender = new MetricsSender(conf.build(), parametersPort, siClientPort, metricsSource);

        metricsSender.run();

        verify(parametersPort).updateMetricsSendTime(any());
    }
}
