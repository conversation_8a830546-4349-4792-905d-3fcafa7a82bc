package fr.enedis.i2r.infra;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import fr.enedis.i2r.comsi.errors.MetricsGatheringException;
import fr.enedis.i2r.infra.metrics.TelegrafJsonlMetricsSourceAdapter;

public class TelegrafJsonlMetricsSourceAdapterTest {
    @TempDir
    Path metricsTempFolder;

    void setupTestFiles(List<String> filesToInclude) throws IOException {
        for (String fileName : filesToInclude) {
            // Chemin source dans resources
            Path source = Paths.get("src/test/resources/metrics-test-files", fileName);
            // Chemin cible dans le dossier temporaire
            Path target = metricsTempFolder.resolve(fileName);

            // Copie du fichier
            Files.copy(source, target);

            // Vérification que le fichier a bien été copié
            assertTrue(Files.exists(target), "Le fichier " + fileName + " n'a pas été copié.");
        }
    }

    @Test
    void le_retriever_est_capable_de_recuperer_tous_les_fichiers_de_metrics_meme_avec_erreurs() throws MetricsGatheringException, IOException {
        setupTestFiles(List.of("two-valid-metrics.jsonl", "three-handled-one-corrupted.jsonl"));
        var metricsSource = new TelegrafJsonlMetricsSourceAdapter(metricsTempFolder.toString());

        var result = metricsSource.getAvailableMetrics();
        assertThat(result).isNotEmpty();
        assertThat(result.size()).isEqualTo(2);
    }

    @Test
    void un_fichier_de_metrics_completement_corrompu_ne_bloque_pas_le_process() throws MetricsGatheringException, IOException {
        setupTestFiles(List.of("two-valid-metrics.jsonl", "three-handled-one-corrupted.jsonl", "corrupted.jsonl"));
        var metricsSource = new TelegrafJsonlMetricsSourceAdapter(metricsTempFolder.toString());

        var result = metricsSource.getAvailableMetrics();
        assertThat(result).isNotEmpty();
        assertThat(result.size()).isEqualTo(2);
    }

}
